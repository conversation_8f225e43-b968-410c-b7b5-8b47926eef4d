# 4.3 节点打包发布

## 🎯 学习目标

掌握自定义节点的打包、发布和维护流程，学会创建专业的节点包，能够将节点分享给社区并进行长期维护。

## 📁 项目结构和配置

### 标准项目结构

```
my_custom_nodes/
├── __init__.py                 # 节点注册入口
├── nodes/                      # 节点实现目录
│   ├── __init__.py
│   ├── basic_nodes.py         # 基础节点
│   ├── advanced_nodes.py      # 高级节点
│   └── utils.py               # 工具函数
├── web/                       # 前端资源
│   ├── js/
│   │   └── my_nodes.js       # JavaScript扩展
│   └── css/
│       └── my_nodes.css      # 样式文件
├── models/                    # 模型文件目录
│   └── .gitkeep
├── examples/                  # 示例工作流
│   ├── basic_workflow.json
│   └── advanced_workflow.json
├── tests/                     # 测试文件
│   ├── __init__.py
│   ├── test_basic_nodes.py
│   └── test_advanced_nodes.py
├── docs/                      # 文档目录
│   ├── README.md
│   ├── INSTALLATION.md
│   └── API.md
├── requirements.txt           # Python依赖
├── setup.py                   # 安装脚本
├── pyproject.toml            # 现代Python项目配置
├── LICENSE                    # 许可证
├── README.md                 # 项目说明
├── CHANGELOG.md              # 更新日志
└── .gitignore                # Git忽略文件
```

### 核心配置文件

**__init__.py**：
```python
"""
My Custom Nodes for ComfyUI
A collection of useful custom nodes for ComfyUI workflows.
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入节点
from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS

# 版本信息
__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

# 导出节点映射
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS']

# 可选：添加初始化逻辑
def initialize():
    """初始化节点包"""
    print(f"Loading My Custom Nodes v{__version__}")
    
    # 检查依赖
    try:
        import torch
        import numpy as np
        print("✓ Core dependencies available")
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return False
    
    return True

# 执行初始化
if not initialize():
    print("Warning: Some features may not work due to missing dependencies")
```

**pyproject.toml**：
```toml
[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "comfyui-my-custom-nodes"
description = "A collection of useful custom nodes for ComfyUI"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "torch>=1.9.0",
    "numpy>=1.21.0",
    "pillow>=8.0.0",
    "requests>=2.25.0",
]
dynamic = ["version"]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=21.0",
    "flake8>=3.9",
    "mypy>=0.910",
]
ml = [
    "scikit-learn>=1.0.0",
    "opencv-python>=4.5.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/comfyui-my-custom-nodes"
Repository = "https://github.com/yourusername/comfyui-my-custom-nodes"
Documentation = "https://github.com/yourusername/comfyui-my-custom-nodes/blob/main/docs/README.md"
"Bug Tracker" = "https://github.com/yourusername/comfyui-my-custom-nodes/issues"

[tool.setuptools_scm]
write_to = "my_custom_nodes/_version.py"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
```

**requirements.txt**：
```txt
# Core dependencies
torch>=1.9.0
numpy>=1.21.0
pillow>=8.0.0
requests>=2.25.0

# Optional ML dependencies
scikit-learn>=1.0.0; extra == "ml"
opencv-python>=4.5.0; extra == "ml"

# Development dependencies
pytest>=6.0; extra == "dev"
black>=21.0; extra == "dev"
flake8>=3.9; extra == "dev"
mypy>=0.910; extra == "dev"
```

## 📦 依赖管理和环境兼容

### 依赖检查和安装

**install.py**：
```python
"""
自动安装脚本
"""
import subprocess
import sys
import os
import importlib.util

def is_package_installed(package_name):
    """检查包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package
        ])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package}: {e}")
        return False

def check_and_install_dependencies():
    """检查并安装依赖"""
    requirements_file = os.path.join(
        os.path.dirname(__file__), "requirements.txt"
    )
    
    if not os.path.exists(requirements_file):
        print("requirements.txt not found")
        return False
    
    # 读取依赖列表
    with open(requirements_file, 'r') as f:
        requirements = f.readlines()
    
    # 过滤和处理依赖
    packages_to_install = []
    for req in requirements:
        req = req.strip()
        if req and not req.startswith('#'):
            # 处理条件依赖
            if ';' in req:
                package, condition = req.split(';', 1)
                package = package.strip()
                # 简单处理，实际可能需要更复杂的逻辑
                if 'extra' not in condition:
                    packages_to_install.append(package)
            else:
                packages_to_install.append(req)
    
    # 检查和安装
    failed_packages = []
    for package in packages_to_install:
        package_name = package.split('>=')[0].split('==')[0].split('<')[0]
        
        if not is_package_installed(package_name):
            print(f"Installing {package}...")
            if not install_package(package):
                failed_packages.append(package)
        else:
            print(f"✓ {package_name} already installed")
    
    if failed_packages:
        print(f"Failed to install: {failed_packages}")
        return False
    
    print("All dependencies installed successfully!")
    return True

if __name__ == "__main__":
    check_and_install_dependencies()
```

### 环境兼容性处理

**compatibility.py**：
```python
"""
环境兼容性检查和处理
"""
import sys
import platform
import torch

def check_python_version():
    """检查Python版本"""
    min_version = (3, 8)
    current_version = sys.version_info[:2]
    
    if current_version < min_version:
        raise RuntimeError(
            f"Python {min_version[0]}.{min_version[1]}+ required, "
            f"but {current_version[0]}.{current_version[1]} found"
        )
    
    return True

def check_torch_compatibility():
    """检查PyTorch兼容性"""
    try:
        import torch
        torch_version = torch.__version__
        
        # 检查CUDA可用性
        cuda_available = torch.cuda.is_available()
        
        print(f"PyTorch version: {torch_version}")
        print(f"CUDA available: {cuda_available}")
        
        if cuda_available:
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU count: {torch.cuda.device_count()}")
        
        return True
    except ImportError:
        print("PyTorch not found")
        return False

def check_system_compatibility():
    """检查系统兼容性"""
    system = platform.system()
    architecture = platform.machine()
    
    print(f"System: {system} {architecture}")
    
    # 检查特定系统的兼容性
    if system == "Windows":
        # Windows特定检查
        pass
    elif system == "Linux":
        # Linux特定检查
        pass
    elif system == "Darwin":  # macOS
        # macOS特定检查
        pass
    
    return True

def run_compatibility_check():
    """运行完整的兼容性检查"""
    checks = [
        ("Python version", check_python_version),
        ("PyTorch compatibility", check_torch_compatibility),
        ("System compatibility", check_system_compatibility),
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            result = check_func()
            results[name] = result
            print(f"✓ {name}: OK")
        except Exception as e:
            results[name] = False
            print(f"✗ {name}: {e}")
    
    return all(results.values())
```

## 📚 文档编写和示例提供

### README.md模板

```markdown
# My Custom Nodes for ComfyUI

A collection of useful custom nodes for ComfyUI workflows.

## Features

- 🎨 **Text Processing Nodes**: Advanced text manipulation and processing
- 🖼️ **Image Analysis Nodes**: Image information extraction and analysis
- 🔧 **Utility Nodes**: Various utility functions for workflow optimization
- 🤖 **ML Integration Nodes**: Machine learning model integration

## Installation

### Method 1: ComfyUI Manager (Recommended)

1. Install [ComfyUI Manager](https://github.com/ltdrdata/ComfyUI-Manager)
2. Search for "My Custom Nodes" in the manager
3. Click install and restart ComfyUI

### Method 2: Manual Installation

1. Clone this repository to your ComfyUI custom_nodes directory:
```bash
cd ComfyUI/custom_nodes
git clone https://github.com/yourusername/comfyui-my-custom-nodes.git
```

2. Install dependencies:
```bash
cd comfyui-my-custom-nodes
pip install -r requirements.txt
```

3. Restart ComfyUI

### Method 3: Automatic Installation

Run the installation script:
```bash
cd ComfyUI/custom_nodes/comfyui-my-custom-nodes
python install.py
```

## Quick Start

1. After installation, you'll find new nodes in the following categories:
   - `text/` - Text processing nodes
   - `image/analysis/` - Image analysis nodes
   - `utils/` - Utility nodes

2. Check the `examples/` directory for sample workflows

## Node Documentation

### Text Processing Nodes

#### SimpleTextProcessor
Processes text with various operations.

**Inputs:**
- `text` (STRING): Input text to process
- `operation` (COMBO): Operation type (uppercase, lowercase, reverse)

**Outputs:**
- `processed_text` (STRING): Processed text result

**Example:**
```json
{
  "text": "Hello World",
  "operation": "uppercase"
}
// Output: "HELLO WORLD"
```

### Image Analysis Nodes

#### ImageInfo
Extracts information from images.

**Inputs:**
- `image` (IMAGE): Input image

**Outputs:**
- `info_text` (STRING): Image information as text
- `width` (INT): Image width
- `height` (INT): Image height
- `batch_size` (INT): Batch size

## Examples

See the `examples/` directory for complete workflow examples:

- `basic_workflow.json` - Basic text processing workflow
- `advanced_workflow.json` - Advanced image analysis workflow

## Development

### Setting up development environment

1. Clone the repository
2. Install development dependencies:
```bash
pip install -e ".[dev]"
```

3. Run tests:
```bash
pytest tests/
```

4. Format code:
```bash
black .
flake8 .
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history.

## Support

- 🐛 [Report bugs](https://github.com/yourusername/comfyui-my-custom-nodes/issues)
- 💡 [Request features](https://github.com/yourusername/comfyui-my-custom-nodes/issues)
- 💬 [Discussions](https://github.com/yourusername/comfyui-my-custom-nodes/discussions)
```

### API文档模板

**docs/API.md**：
```markdown
# API Documentation

## Node Classes

### BaseNode

Base class for all custom nodes.

```python
class BaseNode:
    @classmethod
    def INPUT_TYPES(cls):
        """Define input types and parameters"""
        pass
    
    RETURN_TYPES = ()
    RETURN_NAMES = ()
    FUNCTION = "process"
    CATEGORY = "custom"
    
    def process(self, **kwargs):
        """Main processing function"""
        pass
```

### SimpleTextProcessor

Text processing node with multiple operations.

#### Methods

##### process_text(text: str, operation: str) -> tuple

Process text with specified operation.

**Parameters:**
- `text` (str): Input text to process
- `operation` (str): Operation type ("uppercase", "lowercase", "reverse")

**Returns:**
- `tuple`: Processed text result

**Example:**
```python
node = SimpleTextProcessor()
result = node.process_text("hello", "uppercase")
print(result[0])  # "HELLO"
```

## Utility Functions

### validate_input(data: Any, expected_type: type) -> bool

Validate input data type.

### format_error(error: Exception) -> str

Format error message for display.
```

## 🔄 版本控制和发布流程

### 版本管理策略

**版本号规则**（语义化版本）：
- `MAJOR.MINOR.PATCH`
- `MAJOR`: 不兼容的API更改
- `MINOR`: 向后兼容的功能添加
- `PATCH`: 向后兼容的错误修复

**CHANGELOG.md**：
```markdown
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New feature in development

### Changed
- Improvements to existing features

### Fixed
- Bug fixes

## [1.0.0] - 2024-01-15

### Added
- Initial release
- SimpleTextProcessor node
- ImageInfo node
- Basic utility functions

### Security
- Input validation for all nodes

## [0.9.0] - 2024-01-10

### Added
- Beta release
- Core functionality implementation

[Unreleased]: https://github.com/yourusername/comfyui-my-custom-nodes/compare/v1.0.0...HEAD
[1.0.0]: https://github.com/yourusername/comfyui-my-custom-nodes/compare/v0.9.0...v1.0.0
[0.9.0]: https://github.com/yourusername/comfyui-my-custom-nodes/releases/tag/v0.9.0
```

### 自动化发布脚本

**scripts/release.py**：
```python
"""
自动化发布脚本
"""
import subprocess
import sys
import re
import os
from datetime import datetime

def get_current_version():
    """获取当前版本"""
    try:
        with open("pyproject.toml", "r") as f:
            content = f.read()
            match = re.search(r'version = "([^"]+)"', content)
            if match:
                return match.group(1)
    except FileNotFoundError:
        pass
    
    return "0.0.0"

def update_version(new_version):
    """更新版本号"""
    files_to_update = [
        ("pyproject.toml", r'version = "[^"]+"', f'version = "{new_version}"'),
        ("__init__.py", r'__version__ = "[^"]+"', f'__version__ = "{new_version}"'),
    ]
    
    for filename, pattern, replacement in files_to_update:
        if os.path.exists(filename):
            with open(filename, "r") as f:
                content = f.read()
            
            updated_content = re.sub(pattern, replacement, content)
            
            with open(filename, "w") as f:
                f.write(updated_content)
            
            print(f"Updated {filename}")

def run_tests():
    """运行测试"""
    try:
        subprocess.check_call([sys.executable, "-m", "pytest", "tests/"])
        return True
    except subprocess.CalledProcessError:
        print("Tests failed!")
        return False

def create_git_tag(version):
    """创建Git标签"""
    try:
        subprocess.check_call(["git", "add", "."])
        subprocess.check_call(["git", "commit", "-m", f"Release v{version}"])
        subprocess.check_call(["git", "tag", f"v{version}"])
        print(f"Created git tag v{version}")
        return True
    except subprocess.CalledProcessError:
        print("Failed to create git tag")
        return False

def push_to_github():
    """推送到GitHub"""
    try:
        subprocess.check_call(["git", "push"])
        subprocess.check_call(["git", "push", "--tags"])
        print("Pushed to GitHub")
        return True
    except subprocess.CalledProcessError:
        print("Failed to push to GitHub")
        return False

def main():
    """主发布流程"""
    if len(sys.argv) != 2:
        print("Usage: python release.py <version>")
        sys.exit(1)
    
    new_version = sys.argv[1]
    current_version = get_current_version()
    
    print(f"Current version: {current_version}")
    print(f"New version: {new_version}")
    
    # 确认发布
    confirm = input("Continue with release? (y/N): ")
    if confirm.lower() != 'y':
        print("Release cancelled")
        sys.exit(0)
    
    # 运行测试
    print("Running tests...")
    if not run_tests():
        sys.exit(1)
    
    # 更新版本
    print("Updating version...")
    update_version(new_version)
    
    # 创建Git标签
    print("Creating git tag...")
    if not create_git_tag(new_version):
        sys.exit(1)
    
    # 推送到GitHub
    print("Pushing to GitHub...")
    if not push_to_github():
        sys.exit(1)
    
    print(f"Release v{new_version} completed successfully!")

if __name__ == "__main__":
    main()
```

## 🌐 社区分享和维护

### GitHub Actions工作流

**.github/workflows/test.yml**：
```yaml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics
    
    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=my_custom_nodes --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

**.github/workflows/release.yml**：
```yaml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build package
      run: python -m build
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false
```

### 社区维护指南

**CONTRIBUTING.md**：
```markdown
# Contributing Guide

Thank you for your interest in contributing to My Custom Nodes!

## Development Setup

1. Fork the repository
2. Clone your fork:
```bash
git clone https://github.com/yourusername/comfyui-my-custom-nodes.git
cd comfyui-my-custom-nodes
```

3. Install development dependencies:
```bash
pip install -e ".[dev]"
```

## Code Style

- Use Black for code formatting: `black .`
- Follow PEP 8 guidelines
- Add type hints where possible
- Write docstrings for all public functions

## Testing

- Write tests for new functionality
- Run tests before submitting: `pytest tests/`
- Ensure code coverage remains high

## Pull Request Process

1. Create a feature branch from `develop`
2. Make your changes
3. Add tests for new functionality
4. Update documentation if needed
5. Submit a pull request

## Issue Reporting

When reporting issues, please include:
- ComfyUI version
- Python version
- Operating system
- Steps to reproduce
- Error messages (if any)
```

## 📝 小结

节点打包发布的关键要素：

- **项目结构**：标准化的目录组织和配置文件
- **依赖管理**：兼容性检查和自动安装
- **文档编写**：完整的使用说明和API文档
- **版本控制**：语义化版本和自动化发布
- **社区维护**：持续集成和贡献指南

遵循这些最佳实践可以创建高质量、易维护的节点包。

---

**下一章**：[第五章：实战项目案例](../05-practical-projects/README.md)
