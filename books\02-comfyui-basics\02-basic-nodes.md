# 2.2 基础节点使用

## 🎯 学习目标

掌握ComfyUI中各种基础节点的使用方法，理解节点间的连接关系，能够构建基本的AI生图工作流。

## 📚 节点系统概述

### 什么是节点？

在ComfyUI中，节点（Node）是执行特定功能的基本单元：
- 每个节点负责一个特定任务
- 节点通过连线传递数据
- 组合不同节点形成完整工作流

### 节点的基本结构

```
┌─────────────────┐
│   节点标题      │
├─────────────────┤
│ 输入参数1       │ ← 输入端口
│ 输入参数2       │
├─────────────────┤
│ 输出结果1       │ ← 输出端口
│ 输出结果2       │
└─────────────────┘
```

## 🔧 模型加载节点详解

### 1. CheckpointLoaderSimple - 基础模型加载器

**功能**：加载Stable Diffusion主模型

**输入参数**：
- `ckpt_name`：模型文件名（.safetensors或.ckpt格式）

**输出**：
- `MODEL`：扩散模型
- `CLIP`：文本编码器
- `VAE`：变分自编码器

**使用示例**：
```python
# 节点配置
{
    "class_type": "CheckpointLoaderSimple",
    "inputs": {
        "ckpt_name": "v1-5-pruned-emaonly.safetensors"
    }
}
```

**常用模型推荐**：
- `v1-5-pruned-emaonly.safetensors`：经典SD1.5模型
- `sd_xl_base_1.0.safetensors`：SDXL基础模型
- `dreamshaper_8.safetensors`：高质量社区模型

### 2. LoraLoader - LoRA模型加载器

**功能**：加载LoRA微调模型

**输入参数**：
- `model`：基础模型（来自CheckpointLoader）
- `clip`：CLIP编码器
- `lora_name`：LoRA文件名
- `strength_model`：模型强度（0.0-2.0）
- `strength_clip`：CLIP强度（0.0-2.0）

**输出**：
- `MODEL`：增强后的模型
- `CLIP`：增强后的CLIP

**使用技巧**：
- 强度通常设置为0.7-1.0
- 可以串联多个LoraLoader使用多个LoRA
- 强度过高可能导致过拟合

### 3. VAELoader - VAE加载器

**功能**：单独加载VAE模型

**输入参数**：
- `vae_name`：VAE文件名

**输出**：
- `VAE`：变分自编码器

**使用场景**：
- 使用更高质量的VAE替换模型自带的VAE
- 常用VAE：`vae-ft-mse-840000-ema-pruned.safetensors`

## 📝 文本编码和处理节点

### 1. CLIPTextEncode - 文本编码器

**功能**：将文本提示转换为向量表示

**输入参数**：
- `text`：文本提示词
- `clip`：CLIP编码器

**输出**：
- `CONDITIONING`：条件向量

**提示词编写技巧**：
```
正面提示词示例：
"masterpiece, best quality, 1girl, beautiful face, detailed eyes, 
long hair, elegant dress, soft lighting, photorealistic"

负面提示词示例：
"worst quality, low quality, blurry, distorted, deformed, 
bad anatomy, bad hands, watermark, signature"
```

### 2. CLIPTextEncodeSDXL - SDXL文本编码器

**功能**：专门为SDXL模型设计的文本编码器

**输入参数**：
- `text_g`：全局文本（主要描述）
- `text_l`：局部文本（细节描述）
- `clip`：SDXL的CLIP编码器

**使用建议**：
- `text_g`：写主要内容和风格
- `text_l`：写具体细节和质量词

### 3. ConditioningCombine - 条件组合器

**功能**：组合多个条件向量

**输入参数**：
- `conditioning_1`：第一个条件
- `conditioning_2`：第二个条件

**输出**：
- `CONDITIONING`：组合后的条件

**应用场景**：
- 组合正面和负面提示
- 混合不同的条件控制

## 🎨 采样和生成节点

### 1. KSampler - 核心采样器

**功能**：执行扩散采样过程，生成图像

**输入参数**：
- `model`：扩散模型
- `positive`：正面条件
- `negative`：负面条件
- `latent_image`：初始潜在图像
- `seed`：随机种子
- `steps`：采样步数
- `cfg`：引导强度
- `sampler_name`：采样器类型
- `scheduler`：调度器类型
- `denoise`：去噪强度

**输出**：
- `LATENT`：生成的潜在图像

**参数详解**：
```python
# 推荐参数设置
{
    "seed": 42,                    # 固定种子确保可重现
    "steps": 30,                   # 平衡质量和速度
    "cfg": 8.0,                    # 适中的引导强度
    "sampler_name": "dpm++_2m",    # 高质量采样器
    "scheduler": "karras",         # 优化的调度器
    "denoise": 1.0                 # 完全去噪
}
```

### 2. KSamplerAdvanced - 高级采样器

**功能**：提供更多控制选项的采样器

**额外参数**：
- `add_noise`：是否添加噪声
- `noise_seed`：噪声种子
- `start_at_step`：开始步数
- `end_at_step`：结束步数
- `return_with_leftover_noise`：是否保留噪声

**使用场景**：
- 分阶段采样
- 噪声控制
- 高级工作流设计

### 3. EmptyLatentImage - 空白潜在图像

**功能**：创建指定尺寸的空白潜在图像

**输入参数**：
- `width`：图像宽度
- `height`：图像高度
- `batch_size`：批次大小

**输出**：
- `LATENT`：空白潜在图像

**常用尺寸**：
- 512×512：SD1.5标准尺寸
- 1024×1024：SDXL标准尺寸
- 768×512：横向构图
- 512×768：纵向构图

## 🖼️ 图像处理和输出节点

### 1. VAEDecode - VAE解码器

**功能**：将潜在图像解码为可视图像

**输入参数**：
- `samples`：潜在图像
- `vae`：VAE解码器

**输出**：
- `IMAGE`：解码后的图像

### 2. VAEEncode - VAE编码器

**功能**：将图像编码为潜在表示

**输入参数**：
- `pixels`：输入图像
- `vae`：VAE编码器

**输出**：
- `LATENT`：编码后的潜在图像

**使用场景**：
- img2img工作流
- 图像修复
- 风格转换

### 3. LoadImage - 图像加载器

**功能**：从文件加载图像

**输入参数**：
- `image`：图像文件名

**输出**：
- `IMAGE`：加载的图像
- `MASK`：图像遮罩（如果有）

**支持格式**：
- PNG、JPG、JPEG、WEBP、BMP

### 4. SaveImage - 图像保存器

**功能**：保存生成的图像

**输入参数**：
- `images`：要保存的图像
- `filename_prefix`：文件名前缀

**输出**：
- 图像文件保存到output文件夹

**文件命名规则**：
```
{filename_prefix}_{timestamp}_{counter}.png
```

### 5. PreviewImage - 图像预览器

**功能**：在界面中预览图像

**输入参数**：
- `images`：要预览的图像

**特点**：
- 不保存文件，仅预览
- 适合调试和快速查看

## 🎛️ 条件控制节点

### 1. ConditioningSetArea - 区域条件设置

**功能**：为图像特定区域设置条件

**输入参数**：
- `conditioning`：基础条件
- `width`：区域宽度
- `height`：区域高度
- `x`：X坐标
- `y`：Y坐标
- `strength`：强度

**应用场景**：
- 局部控制
- 区域性风格应用

### 2. ConditioningSetMask - 遮罩条件设置

**功能**：使用遮罩控制条件应用区域

**输入参数**：
- `conditioning`：基础条件
- `mask`：遮罩图像
- `strength`：强度
- `set_cond_area`：条件区域设置

### 3. ControlNetLoader - ControlNet加载器

**功能**：加载ControlNet控制模型

**输入参数**：
- `control_net_name`：ControlNet模型名

**输出**：
- `CONTROL_NET`：ControlNet模型

### 4. ControlNetApply - ControlNet应用器

**功能**：将ControlNet应用到条件上

**输入参数**：
- `conditioning`：基础条件
- `control_net`：ControlNet模型
- `image`：控制图像
- `strength`：控制强度

**输出**：
- `CONDITIONING`：增强后的条件

## 🔗 节点连接规则

### 数据类型匹配

不同颜色的连接线代表不同数据类型：
- **紫色**：MODEL（模型）
- **黄色**：CLIP（文本编码器）
- **红色**：VAE（变分自编码器）
- **绿色**：CONDITIONING（条件）
- **白色**：LATENT（潜在图像）
- **蓝色**：IMAGE（图像）

### 连接原则

1. **输出到输入**：只能从输出端口连接到输入端口
2. **类型匹配**：连接的数据类型必须匹配
3. **一对多**：一个输出可以连接多个输入
4. **多对一**：一个输入只能连接一个输出

## 🛠️ 实践练习

### 练习1：基础txt2img工作流

构建最简单的文本生图工作流：

```
CheckpointLoaderSimple → MODEL → KSampler
                      → CLIP → CLIPTextEncode → CONDITIONING
                      → VAE → VAEDecode

EmptyLatentImage → LATENT → KSampler → LATENT → VAEDecode → IMAGE → SaveImage
```

### 练习2：添加LoRA的工作流

在基础工作流上添加LoRA：

```
CheckpointLoaderSimple → LoraLoader → MODEL → KSampler
                                   → CLIP → CLIPTextEncode
```

### 练习3：负面提示词工作流

添加负面提示词控制：

```
CLIPTextEncode (positive) → CONDITIONING → KSampler
CLIPTextEncode (negative) → CONDITIONING → KSampler
```

## 📝 小结

掌握基础节点是使用ComfyUI的关键：

- **模型加载节点**：提供AI生成的基础能力
- **文本编码节点**：将提示词转换为AI理解的格式
- **采样节点**：执行实际的图像生成过程
- **图像处理节点**：处理输入输出图像
- **条件控制节点**：提供精确的生成控制

理解这些节点的功能和连接方式，就能构建出强大的AI生图工作流。

## 🎯 练习题

1. **基础题**：创建一个包含正负面提示词的基础txt2img工作流
2. **进阶题**：添加LoRA和自定义VAE到工作流中
3. **挑战题**：设计一个可以批量生成不同尺寸图像的工作流

---

**下一节**：[2.3 工作流设计实践](./03-workflows.md)
