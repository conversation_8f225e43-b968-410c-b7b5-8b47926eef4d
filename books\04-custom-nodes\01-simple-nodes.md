# 4.1 简单节点开发

## 🎯 学习目标

掌握ComfyUI自定义节点的基础开发技能，能够创建简单但实用的自定义节点，理解节点的工作原理和开发流程。

## 🛠️ 节点开发环境搭建

### 开发环境要求

**基础环境**：
- Python 3.8+
- ComfyUI已安装并能正常运行
- 代码编辑器（推荐VS Code）
- Git版本控制

**推荐工具**：
```bash
# 安装开发工具
pip install black          # 代码格式化
pip install flake8         # 代码检查
pip install pytest         # 单元测试
```

### 项目结构设置

在ComfyUI的`custom_nodes`目录下创建项目：

```
custom_nodes/
└── my_custom_nodes/
    ├── __init__.py
    ├── nodes.py
    ├── requirements.txt
    ├── README.md
    └── examples/
        └── workflow_example.json
```

**目录说明**：
- `__init__.py`：节点注册入口
- `nodes.py`：节点实现代码
- `requirements.txt`：依赖包列表
- `README.md`：使用说明
- `examples/`：示例工作流

## 🎨 第一个自定义节点

### 最简单的节点示例

创建一个文本处理节点：

**nodes.py**：
```python
class SimpleTextProcessor:
    """简单文本处理节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        """定义输入类型"""
        return {
            "required": {
                "text": ("STRING", {
                    "multiline": True,
                    "default": "Hello World"
                }),
                "operation": (["uppercase", "lowercase", "reverse"], {
                    "default": "uppercase"
                }),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("processed_text",)
    FUNCTION = "process_text"
    CATEGORY = "text"
    
    def process_text(self, text, operation):
        """处理文本的主要函数"""
        if operation == "uppercase":
            result = text.upper()
        elif operation == "lowercase":
            result = text.lower()
        elif operation == "reverse":
            result = text[::-1]
        else:
            result = text
            
        return (result,)

# 节点映射
NODE_CLASS_MAPPINGS = {
    "SimpleTextProcessor": SimpleTextProcessor
}

# 节点显示名称
NODE_DISPLAY_NAME_MAPPINGS = {
    "SimpleTextProcessor": "Simple Text Processor"
}
```

**__init__.py**：
```python
from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS

# 导出节点映射
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS']
```

### 节点测试

1. **重启ComfyUI**：保存文件后重启ComfyUI
2. **查找节点**：在节点菜单中找到"text"分类
3. **添加节点**：拖拽"Simple Text Processor"到画布
4. **测试功能**：输入文本，选择操作，查看结果

## 📚 基础节点类型和接口

### 输入类型定义

**INPUT_TYPES方法详解**：
```python
@classmethod
def INPUT_TYPES(cls):
    return {
        "required": {
            # 必需参数
            "param1": ("TYPE", {"default": value}),
        },
        "optional": {
            # 可选参数
            "param2": ("TYPE", {"default": value}),
        },
        "hidden": {
            # 隐藏参数（通常用于内部状态）
            "unique_id": "UNIQUE_ID",
            "extra_pnginfo": "EXTRA_PNGINFO",
        }
    }
```

### 常用数据类型

**基础类型**：
```python
# 字符串类型
"text": ("STRING", {
    "multiline": True,      # 多行输入
    "default": "默认值",
    "placeholder": "提示文本"
})

# 整数类型
"count": ("INT", {
    "default": 1,
    "min": 0,              # 最小值
    "max": 100,            # 最大值
    "step": 1              # 步长
})

# 浮点数类型
"strength": ("FLOAT", {
    "default": 1.0,
    "min": 0.0,
    "max": 2.0,
    "step": 0.1,
    "round": 0.01          # 精度
})

# 布尔类型
"enabled": ("BOOLEAN", {
    "default": True
})

# 选择列表
"mode": (["option1", "option2", "option3"], {
    "default": "option1"
})
```

**ComfyUI特定类型**：
```python
# 图像类型
"image": ("IMAGE",)

# 潜在图像类型
"latent": ("LATENT",)

# 模型类型
"model": ("MODEL",)

# 条件类型
"conditioning": ("CONDITIONING",)

# CLIP类型
"clip": ("CLIP",)

# VAE类型
"vae": ("VAE",)
```

### 输出类型定义

```python
class MyNode:
    # 返回类型元组
    RETURN_TYPES = ("STRING", "INT", "FLOAT")
    
    # 返回名称（可选，用于显示）
    RETURN_NAMES = ("text_result", "number_result", "float_result")
    
    # 处理函数名
    FUNCTION = "process"
    
    # 节点分类
    CATEGORY = "my_nodes"
    
    # 输出节点标识（用于最终输出）
    OUTPUT_NODE = False
    
    def process(self, ...):
        # 必须返回与RETURN_TYPES对应的元组
        return (text_result, number_result, float_result)
```

## 🔧 节点注册和加载机制

### 注册流程

**1. 节点类定义**：
```python
class MyCustomNode:
    @classmethod
    def INPUT_TYPES(cls):
        # 定义输入
        pass
    
    RETURN_TYPES = (...)
    FUNCTION = "main_function"
    CATEGORY = "custom"
    
    def main_function(self, ...):
        # 实现功能
        pass
```

**2. 映射注册**：
```python
# 节点类映射
NODE_CLASS_MAPPINGS = {
    "MyCustomNode": MyCustomNode,
    "AnotherNode": AnotherNode,
}

# 显示名称映射
NODE_DISPLAY_NAME_MAPPINGS = {
    "MyCustomNode": "My Custom Node",
    "AnotherNode": "Another Node",
}
```

**3. 模块导出**：
```python
# __init__.py
from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS

__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS']
```

### 加载机制

ComfyUI启动时的加载过程：

1. **扫描目录**：扫描`custom_nodes`目录
2. **导入模块**：导入每个子目录的`__init__.py`
3. **注册节点**：收集`NODE_CLASS_MAPPINGS`
4. **构建菜单**：根据`CATEGORY`构建节点菜单
5. **准备就绪**：节点可在界面中使用

## 🐛 调试和测试方法

### 基础调试技巧

**1. 日志输出**：
```python
import logging

class MyNode:
    def process(self, input_data):
        # 使用print进行简单调试
        print(f"Processing input: {input_data}")
        
        # 使用logging进行正式日志
        logging.info(f"Node processing: {input_data}")
        
        try:
            result = self.do_processing(input_data)
            return (result,)
        except Exception as e:
            logging.error(f"Processing failed: {e}")
            raise
```

**2. 异常处理**：
```python
def process(self, input_data):
    try:
        # 验证输入
        if not input_data:
            raise ValueError("Input data cannot be empty")
        
        # 处理逻辑
        result = self.process_data(input_data)
        
        # 验证输出
        if result is None:
            raise RuntimeError("Processing returned None")
        
        return (result,)
        
    except Exception as e:
        # 记录错误并重新抛出
        print(f"Error in {self.__class__.__name__}: {e}")
        raise
```

### 单元测试

**创建测试文件** `test_nodes.py`：
```python
import unittest
from nodes import SimpleTextProcessor

class TestSimpleTextProcessor(unittest.TestCase):
    
    def setUp(self):
        self.node = SimpleTextProcessor()
    
    def test_uppercase(self):
        result = self.node.process_text("hello", "uppercase")
        self.assertEqual(result[0], "HELLO")
    
    def test_lowercase(self):
        result = self.node.process_text("HELLO", "lowercase")
        self.assertEqual(result[0], "hello")
    
    def test_reverse(self):
        result = self.node.process_text("hello", "reverse")
        self.assertEqual(result[0], "olleh")

if __name__ == '__main__':
    unittest.main()
```

**运行测试**：
```bash
cd custom_nodes/my_custom_nodes
python -m pytest test_nodes.py -v
```

### 集成测试

**创建测试工作流**：
```python
def create_test_workflow():
    """创建测试工作流"""
    workflow = {
        "1": {
            "class_type": "SimpleTextProcessor",
            "inputs": {
                "text": "Test Input",
                "operation": "uppercase"
            }
        }
    }
    return workflow

def test_workflow_execution():
    """测试工作流执行"""
    from execution import PromptExecutor
    
    workflow = create_test_workflow()
    executor = PromptExecutor()
    
    try:
        result = executor.execute(workflow)
        print(f"Workflow executed successfully: {result}")
        return True
    except Exception as e:
        print(f"Workflow execution failed: {e}")
        return False
```

## 📝 实践示例

### 示例1：数学计算节点

```python
class MathCalculator:
    """数学计算节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "a": ("FLOAT", {"default": 0.0}),
                "b": ("FLOAT", {"default": 0.0}),
                "operation": (["add", "subtract", "multiply", "divide"], {
                    "default": "add"
                }),
            }
        }
    
    RETURN_TYPES = ("FLOAT",)
    RETURN_NAMES = ("result",)
    FUNCTION = "calculate"
    CATEGORY = "math"
    
    def calculate(self, a, b, operation):
        if operation == "add":
            result = a + b
        elif operation == "subtract":
            result = a - b
        elif operation == "multiply":
            result = a * b
        elif operation == "divide":
            if b == 0:
                raise ValueError("Cannot divide by zero")
            result = a / b
        
        return (result,)
```

### 示例2：图像信息节点

```python
import torch

class ImageInfo:
    """获取图像信息的节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "image": ("IMAGE",),
            }
        }
    
    RETURN_TYPES = ("STRING", "INT", "INT", "INT")
    RETURN_NAMES = ("info_text", "width", "height", "batch_size")
    FUNCTION = "get_info"
    CATEGORY = "image/analysis"
    
    def get_info(self, image):
        # image是torch.Tensor，形状为[batch, height, width, channels]
        batch_size, height, width, channels = image.shape
        
        info_text = f"Image: {width}x{height}, Batch: {batch_size}, Channels: {channels}"
        
        return (info_text, width, height, batch_size)
```

### 示例3：条件控制节点

```python
class ConditionalSwitch:
    """条件切换节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "condition": ("BOOLEAN", {"default": True}),
                "if_true": ("*",),
                "if_false": ("*",),
            }
        }
    
    RETURN_TYPES = ("*",)
    RETURN_NAMES = ("output",)
    FUNCTION = "switch"
    CATEGORY = "logic"
    
    def switch(self, condition, if_true, if_false):
        if condition:
            return (if_true,)
        else:
            return (if_false,)
```

## 📋 开发最佳实践

### 代码规范

1. **命名规范**：
   - 类名使用PascalCase
   - 函数名使用snake_case
   - 常量使用UPPER_CASE

2. **文档字符串**：
```python
class MyNode:
    """
    节点功能的简要描述
    
    这个节点用于...
    """
    
    def process(self, input_data):
        """
        处理输入数据
        
        Args:
            input_data: 输入数据描述
            
        Returns:
            tuple: 返回结果描述
        """
        pass
```

3. **错误处理**：
   - 验证输入参数
   - 提供有意义的错误信息
   - 优雅地处理异常情况

### 性能考虑

1. **避免重复计算**：缓存计算结果
2. **内存管理**：及时释放大对象
3. **批处理支持**：支持批量数据处理

## 🎯 练习项目

### 练习1：文件操作节点
创建一个读取文本文件的节点

### 练习2：随机数生成节点
创建一个生成随机数的节点

### 练习3：时间戳节点
创建一个生成当前时间戳的节点

## 📝 小结

简单节点开发的关键要点：

- **环境搭建**：正确配置开发环境
- **基础结构**：理解节点的基本结构
- **类型系统**：掌握输入输出类型定义
- **注册机制**：了解节点注册和加载流程
- **调试测试**：建立有效的调试和测试方法

掌握这些基础知识后，你就可以开发出实用的自定义节点了。

---

**下一节**：[4.2 复杂节点开发](./02-complex-nodes.md)
