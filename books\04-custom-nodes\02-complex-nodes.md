# 4.2 复杂节点开发

## 🎯 学习目标

掌握高级自定义节点开发技术，包括异步处理、进度回调、错误管理、资源优化和外部库集成，能够开发生产级的复杂节点。

## 🚀 高级节点功能实现

### 动态输入节点

创建可以动态添加输入的节点：

```python
class DynamicInputNode:
    """支持动态输入数量的节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "input_count": ("INT", {
                    "default": 2,
                    "min": 1,
                    "max": 10,
                    "step": 1
                }),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("combined_result",)
    FUNCTION = "combine_inputs"
    CATEGORY = "advanced"
    
    def combine_inputs(self, input_count, **kwargs):
        """动态处理多个输入"""
        results = []
        
        # 动态获取输入
        for i in range(input_count):
            input_key = f"input_{i}"
            if input_key in kwargs:
                results.append(str(kwargs[input_key]))
        
        combined = " | ".join(results)
        return (combined,)
    
    @classmethod
    def IS_CHANGED(cls, input_count, **kwargs):
        """检查输入是否改变"""
        # 返回唯一标识，用于缓存控制
        inputs_hash = hash(tuple(sorted(kwargs.items())))
        return f"{input_count}_{inputs_hash}"
```

### 条件执行节点

```python
class ConditionalExecutor:
    """条件执行节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "condition": ("STRING", {"default": ""}),
                "condition_type": (["equals", "contains", "starts_with", "regex"], {
                    "default": "equals"
                }),
                "compare_value": ("STRING", {"default": ""}),
                "execute_if_true": ("BOOLEAN", {"default": True}),
            },
            "optional": {
                "input_data": ("*",),
                "fallback_data": ("*",),
            }
        }
    
    RETURN_TYPES = ("*", "BOOLEAN")
    RETURN_NAMES = ("output", "condition_result")
    FUNCTION = "conditional_execute"
    CATEGORY = "logic"
    
    def conditional_execute(self, condition, condition_type, compare_value, 
                          execute_if_true, input_data=None, fallback_data=None):
        """根据条件执行不同逻辑"""
        import re
        
        # 评估条件
        condition_met = False
        
        if condition_type == "equals":
            condition_met = condition == compare_value
        elif condition_type == "contains":
            condition_met = compare_value in condition
        elif condition_type == "starts_with":
            condition_met = condition.startswith(compare_value)
        elif condition_type == "regex":
            try:
                condition_met = bool(re.search(compare_value, condition))
            except re.error:
                condition_met = False
        
        # 根据条件选择输出
        if (condition_met and execute_if_true) or (not condition_met and not execute_if_true):
            output = input_data if input_data is not None else "Condition met"
        else:
            output = fallback_data if fallback_data is not None else "Condition not met"
        
        return (output, condition_met)
```

### 状态管理节点

```python
class StatefulNode:
    """带状态管理的节点"""
    
    def __init__(self):
        self.state = {}
        self.history = []
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "action": (["set", "get", "clear", "history"], {
                    "default": "set"
                }),
                "key": ("STRING", {"default": "default_key"}),
            },
            "optional": {
                "value": ("*",),
            }
        }
    
    RETURN_TYPES = ("*", "STRING")
    RETURN_NAMES = ("result", "status")
    FUNCTION = "manage_state"
    CATEGORY = "state"
    
    def manage_state(self, action, key, value=None):
        """管理节点状态"""
        if action == "set":
            if value is not None:
                self.state[key] = value
                self.history.append(f"Set {key} = {value}")
                return (value, f"Set {key}")
            else:
                return (None, "No value provided for set operation")
        
        elif action == "get":
            result = self.state.get(key, None)
            status = f"Got {key}" if result is not None else f"Key {key} not found"
            return (result, status)
        
        elif action == "clear":
            if key in self.state:
                del self.state[key]
                self.history.append(f"Cleared {key}")
                return (None, f"Cleared {key}")
            else:
                return (None, f"Key {key} not found")
        
        elif action == "history":
            history_str = "\n".join(self.history[-10:])  # 最近10条记录
            return (history_str, "History retrieved")
```

## ⚡ 异步处理和进度回调

### 异步处理节点

```python
import asyncio
import threading
from typing import Callable, Optional

class AsyncProcessingNode:
    """异步处理节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "data": ("STRING", {"default": ""}),
                "processing_time": ("FLOAT", {
                    "default": 2.0,
                    "min": 0.1,
                    "max": 10.0,
                    "step": 0.1
                }),
                "enable_progress": ("BOOLEAN", {"default": True}),
            }
        }
    
    RETURN_TYPES = ("STRING", "FLOAT")
    RETURN_NAMES = ("processed_data", "processing_time")
    FUNCTION = "async_process"
    CATEGORY = "async"
    
    def __init__(self):
        self.progress_callback: Optional[Callable] = None
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    async def _async_processing(self, data: str, processing_time: float, 
                               enable_progress: bool) -> tuple:
        """异步处理逻辑"""
        steps = 10
        step_time = processing_time / steps
        
        processed_data = ""
        
        for i in range(steps):
            # 模拟处理步骤
            await asyncio.sleep(step_time)
            processed_data += f"Step {i+1}: {data[:10]}...\n"
            
            # 更新进度
            if enable_progress and self.progress_callback:
                progress = (i + 1) / steps
                self.progress_callback(progress, f"Processing step {i+1}/{steps}")
        
        return (processed_data, processing_time)
    
    def async_process(self, data: str, processing_time: float, enable_progress: bool):
        """同步包装的异步处理"""
        import time
        start_time = time.time()
        
        # 在新的事件循环中运行异步代码
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(
                self._async_processing(data, processing_time, enable_progress)
            )
            loop.close()
        except Exception as e:
            raise RuntimeError(f"Async processing failed: {e}")
        
        actual_time = time.time() - start_time
        return (result[0], actual_time)
```

### 进度回调实现

```python
class ProgressCallbackNode:
    """带进度回调的长时间处理节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "iterations": ("INT", {
                    "default": 100,
                    "min": 10,
                    "max": 1000,
                    "step": 10
                }),
                "delay_per_iteration": ("FLOAT", {
                    "default": 0.1,
                    "min": 0.01,
                    "max": 1.0,
                    "step": 0.01
                }),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("result",)
    FUNCTION = "process_with_progress"
    CATEGORY = "progress"
    
    def process_with_progress(self, iterations: int, delay_per_iteration: float):
        """带进度显示的处理"""
        import time
        
        results = []
        
        for i in range(iterations):
            # 模拟处理
            time.sleep(delay_per_iteration)
            results.append(f"Iteration {i+1}")
            
            # 计算并报告进度
            progress = (i + 1) / iterations
            
            # 如果ComfyUI支持进度回调，在这里调用
            # 这是一个假设的API，实际实现可能不同
            if hasattr(self, 'update_progress'):
                self.update_progress(progress, f"Processing {i+1}/{iterations}")
            
            # 控制台输出进度（用于调试）
            if (i + 1) % 10 == 0 or i == iterations - 1:
                print(f"Progress: {progress:.1%} ({i+1}/{iterations})")
        
        result = f"Completed {iterations} iterations"
        return (result,)
```

## 🛡️ 错误处理和异常管理

### 健壮的错误处理

```python
import traceback
import logging
from typing import Any, Tuple, Optional

class RobustProcessingNode:
    """健壮的处理节点，包含完整错误处理"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "input_data": ("STRING", {"default": ""}),
                "error_handling": (["strict", "graceful", "ignore"], {
                    "default": "graceful"
                }),
                "retry_count": ("INT", {
                    "default": 3,
                    "min": 0,
                    "max": 10
                }),
            },
            "optional": {
                "fallback_value": ("STRING", {"default": "Error occurred"}),
            }
        }
    
    RETURN_TYPES = ("STRING", "BOOLEAN", "STRING")
    RETURN_NAMES = ("result", "success", "error_message")
    FUNCTION = "robust_process"
    CATEGORY = "robust"
    
    def __init__(self):
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _validate_input(self, input_data: str) -> None:
        """验证输入数据"""
        if not isinstance(input_data, str):
            raise TypeError(f"Expected string, got {type(input_data)}")
        
        if len(input_data.strip()) == 0:
            raise ValueError("Input data cannot be empty")
        
        # 检查特殊字符或格式
        if len(input_data) > 10000:
            raise ValueError("Input data too long (max 10000 characters)")
    
    def _process_data(self, input_data: str) -> str:
        """实际的数据处理逻辑"""
        # 模拟可能失败的处理
        if "error" in input_data.lower():
            raise RuntimeError("Processing failed due to error keyword")
        
        if "timeout" in input_data.lower():
            import time
            time.sleep(5)  # 模拟超时
        
        # 正常处理
        processed = input_data.upper().replace(" ", "_")
        return f"PROCESSED: {processed}"
    
    def _retry_operation(self, operation, *args, max_retries: int = 3, **kwargs):
        """重试机制"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return operation(*args, **kwargs)
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
                
                if attempt < max_retries:
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    self.logger.error(f"All {max_retries + 1} attempts failed")
        
        raise last_exception
    
    def robust_process(self, input_data: str, error_handling: str, 
                      retry_count: int, fallback_value: str = "Error occurred") -> Tuple[str, bool, str]:
        """健壮的处理方法"""
        try:
            # 验证输入
            self._validate_input(input_data)
            
            # 带重试的处理
            if retry_count > 0:
                result = self._retry_operation(
                    self._process_data, 
                    input_data, 
                    max_retries=retry_count
                )
            else:
                result = self._process_data(input_data)
            
            self.logger.info("Processing completed successfully")
            return (result, True, "Success")
        
        except Exception as e:
            error_msg = str(e)
            full_traceback = traceback.format_exc()
            
            self.logger.error(f"Processing failed: {error_msg}")
            self.logger.debug(f"Full traceback: {full_traceback}")
            
            # 根据错误处理策略决定行为
            if error_handling == "strict":
                # 严格模式：重新抛出异常
                raise
            elif error_handling == "graceful":
                # 优雅模式：返回错误信息
                return (fallback_value, False, error_msg)
            elif error_handling == "ignore":
                # 忽略模式：返回原始输入
                return (input_data, False, f"Ignored error: {error_msg}")
            
        except KeyboardInterrupt:
            # 处理用户中断
            self.logger.info("Processing interrupted by user")
            return ("Interrupted", False, "User interrupted")
```

### 资源清理和上下文管理

```python
import contextlib
import tempfile
import os
from typing import Generator, Any

class ResourceManagedNode:
    """资源管理节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "data": ("STRING", {"default": "test data"}),
                "use_temp_file": ("BOOLEAN", {"default": True}),
                "cleanup_on_error": ("BOOLEAN", {"default": True}),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("result",)
    FUNCTION = "process_with_resources"
    CATEGORY = "resources"
    
    @contextlib.contextmanager
    def managed_temp_file(self, cleanup_on_error: bool = True) -> Generator[str, None, None]:
        """管理临时文件的上下文管理器"""
        temp_file = None
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(mode='w+', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            yield temp_path
            
        except Exception as e:
            if cleanup_on_error and temp_file:
                try:
                    os.unlink(temp_path)
                except OSError:
                    pass
            raise
        finally:
            # 清理资源
            if temp_file and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                except OSError as e:
                    print(f"Warning: Could not delete temp file {temp_path}: {e}")
    
    def process_with_resources(self, data: str, use_temp_file: bool, 
                             cleanup_on_error: bool) -> tuple:
        """使用资源管理的处理"""
        if use_temp_file:
            with self.managed_temp_file(cleanup_on_error) as temp_path:
                # 写入临时文件
                with open(temp_path, 'w') as f:
                    f.write(data)
                
                # 从文件读取并处理
                with open(temp_path, 'r') as f:
                    content = f.read()
                
                processed = f"Processed from temp file: {content.upper()}"
                return (processed,)
        else:
            # 直接处理
            processed = f"Processed directly: {data.upper()}"
            return (processed,)
```

## 💾 资源管理和内存优化

### 内存优化节点

```python
import gc
import psutil
import torch
from typing import Optional

class MemoryOptimizedNode:
    """内存优化节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "data_size": ("INT", {
                    "default": 1000,
                    "min": 100,
                    "max": 100000
                }),
                "enable_gc": ("BOOLEAN", {"default": True}),
                "clear_cache": ("BOOLEAN", {"default": True}),
            }
        }
    
    RETURN_TYPES = ("STRING", "FLOAT", "FLOAT")
    RETURN_NAMES = ("result", "memory_before", "memory_after")
    FUNCTION = "memory_optimized_process"
    CATEGORY = "optimization"
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def clear_gpu_cache(self):
        """清理GPU缓存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
    
    def memory_optimized_process(self, data_size: int, enable_gc: bool, 
                               clear_cache: bool) -> tuple:
        """内存优化的处理"""
        # 记录初始内存
        memory_before = self.get_memory_usage()
        
        try:
            # 创建大数据结构（模拟内存密集操作）
            large_data = list(range(data_size))
            
            # 处理数据
            processed_data = [x * 2 for x in large_data]
            result_summary = f"Processed {len(processed_data)} items"
            
            # 显式删除大对象
            del large_data
            del processed_data
            
            # 可选的垃圾回收
            if enable_gc:
                gc.collect()
            
            # 可选的缓存清理
            if clear_cache:
                self.clear_gpu_cache()
            
        except MemoryError as e:
            # 内存不足时的处理
            gc.collect()
            self.clear_gpu_cache()
            raise RuntimeError(f"Memory error during processing: {e}")
        
        # 记录最终内存
        memory_after = self.get_memory_usage()
        
        return (result_summary, memory_before, memory_after)
```

## 🔗 与外部库的集成

### 外部API集成节点

```python
import requests
import json
from typing import Dict, Any, Optional

class ExternalAPINode:
    """外部API集成节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "api_url": ("STRING", {
                    "default": "https://api.example.com/process"
                }),
                "method": (["GET", "POST", "PUT"], {"default": "POST"}),
                "data": ("STRING", {"default": "{}"}),
                "timeout": ("FLOAT", {
                    "default": 30.0,
                    "min": 1.0,
                    "max": 300.0
                }),
            },
            "optional": {
                "headers": ("STRING", {"default": "{}"}),
                "api_key": ("STRING", {"default": ""}),
            }
        }
    
    RETURN_TYPES = ("STRING", "INT", "STRING")
    RETURN_NAMES = ("response_data", "status_code", "error_message")
    FUNCTION = "call_external_api"
    CATEGORY = "external"
    
    def _prepare_headers(self, headers_str: str, api_key: str) -> Dict[str, str]:
        """准备请求头"""
        try:
            headers = json.loads(headers_str) if headers_str else {}
        except json.JSONDecodeError:
            headers = {}
        
        # 添加默认头
        headers.setdefault("Content-Type", "application/json")
        headers.setdefault("User-Agent", "ComfyUI-CustomNode/1.0")
        
        # 添加API密钥
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"
        
        return headers
    
    def _prepare_data(self, data_str: str, method: str) -> Optional[str]:
        """准备请求数据"""
        if method == "GET":
            return None
        
        try:
            # 验证JSON格式
            json.loads(data_str)
            return data_str
        except json.JSONDecodeError:
            # 如果不是JSON，包装为JSON
            return json.dumps({"data": data_str})
    
    def call_external_api(self, api_url: str, method: str, data: str, 
                         timeout: float, headers: str = "", 
                         api_key: str = "") -> tuple:
        """调用外部API"""
        try:
            # 准备请求参数
            request_headers = self._prepare_headers(headers, api_key)
            request_data = self._prepare_data(data, method)
            
            # 发送请求
            response = requests.request(
                method=method,
                url=api_url,
                headers=request_headers,
                data=request_data,
                timeout=timeout
            )
            
            # 处理响应
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return (json.dumps(response_data, indent=2), response.status_code, "")
                except json.JSONDecodeError:
                    return (response.text, response.status_code, "")
            else:
                error_msg = f"API request failed: {response.status_code} {response.reason}"
                return ("", response.status_code, error_msg)
        
        except requests.exceptions.Timeout:
            return ("", 0, f"Request timeout after {timeout} seconds")
        except requests.exceptions.ConnectionError:
            return ("", 0, "Connection error")
        except Exception as e:
            return ("", 0, f"Unexpected error: {str(e)}")
```

### 机器学习库集成

```python
import numpy as np
try:
    import sklearn
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

class MLProcessingNode:
    """机器学习处理节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        inputs = {
            "required": {
                "data": ("STRING", {"default": "1,2,3\n4,5,6\n7,8,9"}),
                "algorithm": (["kmeans", "normalize"], {"default": "kmeans"}),
            }
        }
        
        if SKLEARN_AVAILABLE:
            inputs["required"]["n_clusters"] = ("INT", {
                "default": 2,
                "min": 1,
                "max": 10
            })
        
        return inputs
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("result", "status")
    FUNCTION = "ml_process"
    CATEGORY = "ml"
    
    def parse_csv_data(self, data_str: str) -> np.ndarray:
        """解析CSV格式数据"""
        lines = data_str.strip().split('\n')
        data_list = []
        
        for line in lines:
            row = [float(x.strip()) for x in line.split(',')]
            data_list.append(row)
        
        return np.array(data_list)
    
    def ml_process(self, data: str, algorithm: str, n_clusters: int = 2) -> tuple:
        """机器学习处理"""
        if not SKLEARN_AVAILABLE:
            return ("", "Error: scikit-learn not available")
        
        try:
            # 解析数据
            data_array = self.parse_csv_data(data)
            
            if algorithm == "kmeans":
                # K-means聚类
                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                labels = kmeans.fit_predict(data_array)
                
                result = f"Cluster labels: {labels.tolist()}\n"
                result += f"Cluster centers:\n{kmeans.cluster_centers_}"
                status = f"K-means clustering completed with {n_clusters} clusters"
                
            elif algorithm == "normalize":
                # 数据标准化
                scaler = StandardScaler()
                normalized_data = scaler.fit_transform(data_array)
                
                result = f"Normalized data:\n{normalized_data}"
                status = "Data normalization completed"
            
            else:
                result = ""
                status = f"Unknown algorithm: {algorithm}"
            
            return (result, status)
        
        except Exception as e:
            return ("", f"ML processing error: {str(e)}")
```

## 📝 小结

复杂节点开发的关键技术：

- **高级功能**：动态输入、条件执行、状态管理
- **异步处理**：提升用户体验，支持长时间操作
- **错误处理**：健壮的异常管理和重试机制
- **资源管理**：内存优化和资源清理
- **外部集成**：API调用和第三方库集成

掌握这些技术可以开发出生产级的高质量节点。

---

**下一节**：[4.3 节点打包发布](./03-packaging.md)
