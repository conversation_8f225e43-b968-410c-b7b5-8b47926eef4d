# 2.3 工作流设计实践

## 🎯 学习目标

学会设计和构建各种类型的ComfyUI工作流，掌握从基础到高级的工作流设计技巧，能够根据需求定制专属工作流。

## 📚 工作流设计原理

### 什么是工作流？

工作流（Workflow）是由多个节点组成的数据处理管道：
- 定义了数据的流向和处理步骤
- 每个节点执行特定功能
- 节点间通过连线传递数据
- 最终输出期望的结果

### 工作流设计原则

1. **模块化**：每个功能独立成模块
2. **可复用**：设计可重复使用的组件
3. **可扩展**：便于添加新功能
4. **高效性**：避免不必要的计算
5. **可维护**：结构清晰，易于理解

## 🎨 基础txt2img工作流

### 工作流概述

最基础的文本生图工作流，从文本提示生成图像。

### 节点构成

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│CheckpointLoader │───▶│  CLIPTextEncode │───▶│    KSampler     │
│     Simple      │    │   (Positive)    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              ┌─────────────────┐             │
         └─────────────▶│  CLIPTextEncode │─────────────┘
                        │   (Negative)    │
                        └─────────────────┘
                                 
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ EmptyLatentImage│───▶│    KSampler     │───▶│   VAEDecode     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   SaveImage     │
                                               └─────────────────┘
```

### 详细配置

**1. CheckpointLoaderSimple**
```json
{
    "inputs": {
        "ckpt_name": "v1-5-pruned-emaonly.safetensors"
    }
}
```

**2. CLIPTextEncode (Positive)**
```json
{
    "inputs": {
        "text": "masterpiece, best quality, 1girl, beautiful, detailed face, long hair, elegant dress, soft lighting",
        "clip": ["1", 1]
    }
}
```

**3. CLIPTextEncode (Negative)**
```json
{
    "inputs": {
        "text": "worst quality, low quality, blurry, bad anatomy, bad hands, watermark",
        "clip": ["1", 1]
    }
}
```

**4. EmptyLatentImage**
```json
{
    "inputs": {
        "width": 512,
        "height": 512,
        "batch_size": 1
    }
}
```

**5. KSampler**
```json
{
    "inputs": {
        "seed": 42,
        "steps": 30,
        "cfg": 8.0,
        "sampler_name": "dpm++_2m",
        "scheduler": "karras",
        "denoise": 1.0,
        "model": ["1", 0],
        "positive": ["2", 0],
        "negative": ["3", 0],
        "latent_image": ["4", 0]
    }
}
```

**6. VAEDecode**
```json
{
    "inputs": {
        "samples": ["5", 0],
        "vae": ["1", 2]
    }
}
```

**7. SaveImage**
```json
{
    "inputs": {
        "images": ["6", 0],
        "filename_prefix": "txt2img"
    }
}
```

### 使用技巧

- **提示词优化**：使用高质量关键词如"masterpiece", "best quality"
- **负面提示**：添加常见问题词如"bad anatomy", "blurry"
- **参数调节**：CFG 7-12，步数20-50为最佳平衡点

## 🖼️ img2img工作流设计

### 工作流概述

基于输入图像进行风格转换或内容修改的工作流。

### 核心差异

与txt2img的主要区别：
- 使用`LoadImage`加载输入图像
- 使用`VAEEncode`将图像编码为潜在空间
- 调整`denoise`参数控制变化程度

### 节点构成

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LoadImage     │───▶│   VAEEncode     │───▶│    KSampler     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                        ┌─────────────────┐    ┌─────────────────┐
                        │CheckpointLoader │───▶│   VAEDecode     │
                        │     Simple      │    └─────────────────┘
                        └─────────────────┘             │
                                │                ┌─────────────────┐
                        ┌─────────────────┐     │   SaveImage     │
                        │  CLIPTextEncode │     └─────────────────┘
                        │   (Positive)    │
                        └─────────────────┘
```

### 关键参数设置

**VAEEncode配置**：
```json
{
    "inputs": {
        "pixels": ["LoadImage", 0],
        "vae": ["CheckpointLoaderSimple", 2]
    }
}
```

**KSampler配置**：
```json
{
    "inputs": {
        "denoise": 0.7,  // 关键参数：控制变化程度
        "latent_image": ["VAEEncode", 0]
    }
}
```

### denoise参数指南

- **0.1-0.3**：轻微调整，保持原图结构
- **0.4-0.6**：中等变化，风格转换
- **0.7-0.9**：大幅变化，内容重构
- **1.0**：完全重新生成

## 🎮 ControlNet工作流

### 工作流概述

使用ControlNet进行精确结构控制的工作流。

### Canny边缘控制工作流

**预处理节点**：
```
┌─────────────────┐    ┌─────────────────┐
│   LoadImage     │───▶│CannyEdgePreproc │
└─────────────────┘    └─────────────────┘
                                │
                        ┌─────────────────┐
                        │ ControlNetApply │
                        └─────────────────┘
```

**CannyEdgePreprocessor配置**：
```json
{
    "inputs": {
        "image": ["LoadImage", 0],
        "low_threshold": 100,
        "high_threshold": 200
    }
}
```

**ControlNetLoader配置**：
```json
{
    "inputs": {
        "control_net_name": "control_v11p_sd15_canny.pth"
    }
}
```

**ControlNetApply配置**：
```json
{
    "inputs": {
        "conditioning": ["CLIPTextEncode", 0],
        "control_net": ["ControlNetLoader", 0],
        "image": ["CannyEdgePreprocessor", 0],
        "strength": 1.0
    }
}
```

### OpenPose姿态控制工作流

**DWPreprocessor配置**：
```json
{
    "inputs": {
        "image": ["LoadImage", 0],
        "detect_hand": "enable",
        "detect_body": "enable",
        "detect_face": "enable"
    }
}
```

### Depth深度控制工作流

**MiDaS深度预处理**：
```json
{
    "inputs": {
        "image": ["LoadImage", 0],
        "a": 6.28,
        "bg_threshold": 0.1
    }
}
```

### ControlNet强度调节

- **0.5-0.7**：轻度控制，保持灵活性
- **0.8-1.0**：强控制，严格遵循结构
- **1.0+**：极强控制，可能过度约束

## 🎨 LoRA集成工作流

### 单LoRA工作流

```
┌─────────────────┐    ┌─────────────────┐
│CheckpointLoader │───▶│   LoraLoader    │
│     Simple      │    └─────────────────┘
└─────────────────┘             │
                        ┌─────────────────┐
                        │  CLIPTextEncode │
                        └─────────────────┘
```

**LoraLoader配置**：
```json
{
    "inputs": {
        "model": ["CheckpointLoaderSimple", 0],
        "clip": ["CheckpointLoaderSimple", 1],
        "lora_name": "style_lora.safetensors",
        "strength_model": 1.0,
        "strength_clip": 1.0
    }
}
```

### 多LoRA串联工作流

```
CheckpointLoader → LoraLoader1 → LoraLoader2 → LoraLoader3 → KSampler
```

**串联配置示例**：
```json
// LoraLoader1 (风格LoRA)
{
    "inputs": {
        "lora_name": "anime_style.safetensors",
        "strength_model": 0.8,
        "strength_clip": 0.8
    }
}

// LoraLoader2 (角色LoRA)
{
    "inputs": {
        "model": ["LoraLoader1", 0],
        "clip": ["LoraLoader1", 1],
        "lora_name": "character_lora.safetensors",
        "strength_model": 0.7,
        "strength_clip": 0.7
    }
}

// LoraLoader3 (概念LoRA)
{
    "inputs": {
        "model": ["LoraLoader2", 0],
        "clip": ["LoraLoader2", 1],
        "lora_name": "concept_lora.safetensors",
        "strength_model": 0.6,
        "strength_clip": 0.6
    }
}
```

### LoRA强度调节策略

- **风格LoRA**：0.7-1.0（较强影响）
- **角色LoRA**：0.6-0.9（中等影响）
- **概念LoRA**：0.5-0.8（适度影响）
- **多LoRA叠加**：每个强度适当降低

## 🔄 批量处理工作流

### 批量生成不同种子

**使用PrimitiveNode控制种子**：
```json
{
    "class_type": "PrimitiveNode",
    "inputs": {
        "value": 42
    }
}
```

### 批量处理不同尺寸

**EmptyLatentImage批量配置**：
```json
{
    "inputs": {
        "width": 512,
        "height": 768,
        "batch_size": 4  // 一次生成4张
    }
}
```

### 批量应用不同LoRA

**使用LoRA切换器**：
```python
# 伪代码：LoRA批量切换逻辑
lora_list = [
    "style1.safetensors",
    "style2.safetensors", 
    "style3.safetensors"
]

for lora_name in lora_list:
    # 更新LoraLoader配置
    # 执行生成
    # 保存结果
```

## 🔧 高级工作流技巧

### 1. 条件分支工作流

使用`ConditioningCombine`创建复杂条件：
```json
{
    "inputs": {
        "conditioning_1": ["CLIPTextEncode_positive", 0],
        "conditioning_2": ["ControlNetApply", 0]
    }
}
```

### 2. 多阶段采样工作流

```
KSampler1 (粗采样) → KSampler2 (细采样) → VAEDecode
```

**KSampler1配置**：
```json
{
    "inputs": {
        "steps": 15,
        "denoise": 1.0
    }
}
```

**KSampler2配置**：
```json
{
    "inputs": {
        "steps": 20,
        "denoise": 0.5,
        "latent_image": ["KSampler1", 0]
    }
}
```

### 3. 图像融合工作流

使用`ImageBlend`节点融合多张图像：
```json
{
    "inputs": {
        "image1": ["VAEDecode1", 0],
        "image2": ["VAEDecode2", 0],
        "blend_factor": 0.5,
        "blend_mode": "normal"
    }
}
```

## 📊 工作流优化策略

### 性能优化

1. **减少不必要的节点**：移除未使用的连接
2. **合理设置批次大小**：根据显存调整batch_size
3. **选择合适的采样器**：平衡质量和速度
4. **优化图像尺寸**：避免过大的分辨率

### 质量优化

1. **精心设计提示词**：使用高质量描述词
2. **合理设置参数**：CFG、步数、denoise的平衡
3. **选择合适的模型**：根据需求选择专业模型
4. **使用高质量VAE**：提升最终图像质量

### 可维护性优化

1. **节点命名**：给重要节点添加注释
2. **模块化设计**：将功能相关的节点组织在一起
3. **参数标准化**：使用一致的参数设置
4. **版本管理**：保存不同版本的工作流

## 🎯 实践项目

### 项目1：人像生成工作流

**需求**：生成高质量人像照片
**技术栈**：基础模型 + 人像LoRA + 面部修复

### 项目2：建筑设计工作流

**需求**：根据草图生成建筑效果图
**技术栈**：ControlNet Canny + 建筑风格LoRA

### 项目3：产品展示工作流

**需求**：为产品生成多角度展示图
**技术栈**：img2img + 批量处理 + 后期处理

## 📝 小结

工作流设计是ComfyUI的核心技能：

- **基础工作流**：掌握txt2img和img2img的基本流程
- **控制工作流**：使用ControlNet实现精确控制
- **增强工作流**：集成LoRA提升生成质量
- **批量工作流**：提高生产效率
- **高级技巧**：多阶段、条件分支、图像融合

通过不断实践和优化，你将能够设计出满足各种需求的专业工作流。

## 🎯 练习题

1. **基础练习**：设计一个包含正负面提示词的完整txt2img工作流
2. **进阶练习**：创建一个结合ControlNet和LoRA的复合工作流
3. **挑战练习**：设计一个可以批量生成不同风格图像的自动化工作流

---

**下一章**：[第三章：高级功能应用](../03-advanced-features/README.md)
